from aiogram import Router, F
from aiogram.types import Message, CallbackQuery
from aiogram.filters import CommandStart
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from ..keyboards.main import get_student_main_menu_kb, get_student_menu_by_tariff

router = Router()

class StudentMainStates(StatesGroup):
    main = State()

@router.message(CommandStart())
async def student_start(message: Message, state: FSMContext, user_role: str = None):
    """Начальное меню студента"""
    # Если роль не куратор, показываем меню студента
    if user_role == "student":
        await show_student_main_menu(message, state)

async def show_student_main_menu(message_or_callback: Message | CallbackQuery, state: FSMContext = None, user_role: str = None):
    """Возврат в главное меню студента"""
    # Студенческое меню доступно всем (включая админов и новых пользователей)
    # Не делаем проверку роли, так как это меню по умолчанию

    # Определяем тип объекта и получаем нужные данные
    if isinstance(message_or_callback, CallbackQuery):
        user_id = message_or_callback.from_user.id
        print(f"DEBUG show_student_main_menu: user_role='{user_role}', telegram_id={user_id}")
        print(f"DEBUG: Обработка CallbackQuery")
    else:  # Message
        user_id = message_or_callback.from_user.id
        print(f"DEBUG show_student_main_menu: user_role='{user_role}', telegram_id={user_id}")
        print(f"DEBUG: Обработка Message")

    # Получаем клавиатуру в зависимости от тарифа
    keyboard = await get_student_menu_by_tariff(user_id)

    text = (
        "Привет 👋\n"
        "Здесь ты можешь проходить домашки, прокачивать темы, отслеживать свой прогресс и готовиться к ЕНТ.\n"
        "Ниже — все разделы, которые тебе доступны:"
    )

    # Отправляем сообщение в зависимости от типа объекта
    if isinstance(message_or_callback, CallbackQuery):
        await message_or_callback.message.answer(text, reply_markup=keyboard)
    else:  # Message
        await message_or_callback.answer(text, reply_markup=keyboard)

    if state:
        await state.set_state(StudentMainStates.main)